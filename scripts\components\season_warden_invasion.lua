local _G = GLOBAL
local Class = _G.Class
local TheWorld = _G.TheWorld
local AllPlayers = _G.AllPlayers
local TUNING = _G.TUNING

-- 引入网络变量
local net_bool = _G.net_bool
local net_tinybyte = _G.net_tinybyte

local function IsValidPlayer(p)
    return p and p:IsValid() and p:<PERSON><PERSON><PERSON>("player") and not p:Has<PERSON><PERSON>("playerghost")
end

local SeasonWardenInvasion = Class(function(self, inst)
    self.marker = nil
    self.inst = inst
    self.enabled = (_G.GetModConfigData("invasion_enabled") ~= false)
    self.per_season = _G.GetModConfigData("invasion_count") or TUNING.SEASON_WARDEN_INVASIONS_PER_SEASON or 2
    self.respawn_days = _G.GetModConfigData("invasion_respawn_days") or TUNING.SEASON_WARDEN_INVASION_RESPAWN_DAYS or 2
    self.warn_secs = TUNING.SEASON_WARDEN_INVASION_WARN_SECS or 5
    self.battle_radius = TUNING.SEASON_WARDEN_BATTLE_RADIUS or 30
    self.combat_timeout = TUNING.SEASON_WARDEN_INVASION_COMBAT_TIMEOUT or 300  -- 5分钟战斗超时
    self.hp_mul = _G.GetModConfigData("invasion_hp_mul") or TUNING.SEASON_WARDEN_INVASION_HP_MUL or 0.4
    self.loot_mul = _G.GetModConfigData("invasion_loot_mul") or TUNING.SEASON_WARDEN_INVASION_LOOT_MUL or 0.5

    self.invasions_done = 0
    self.active = false
    self.boss = nil
    self.origin = nil
    self.last_failed_day = nil  -- 记录上次失败的日期，用于同日不再追击
    self.invasion_start_time = nil  -- 记录入侵开始时间，用于超时检测
    self.last_combat_time = nil  -- 记录最后一次战斗时间，用于脱战检测

    -- 网络同步变量
    if not TheWorld.ismastersim then
        self._net_active = net_bool(inst.GUID, "invasion.active", "invasion_active_dirty")
        self._net_invasions_done = net_tinybyte(inst.GUID, "invasion.done", "invasion_done_dirty")

        -- 客户端监听网络同步事件
        inst:ListenForEvent("invasion_active_dirty", function()
            self.active = self._net_active:value()
        end)

        inst:ListenForEvent("invasion_done_dirty", function()
            self.invasions_done = self._net_invasions_done:value()
        end)

        return
    end

    -- 服务端初始化网络变量
    self._net_active = net_bool(inst.GUID, "invasion.active", "invasion_active_dirty")
    self._net_invasions_done = net_tinybyte(inst.GUID, "invasion.done", "invasion_done_dirty")

    self._net_active:set(false)
    self._net_invasions_done:set(0)

    inst:WatchWorldState("season", function() self:OnSeasonChanged() end)
    inst:DoTaskInTime(0, function() self:OnSeasonChanged(true) end)
end)

function SeasonWardenInvasion:OnSeasonChanged(initial)
    if not TheWorld.ismastersim then return end
    if not self.enabled then return end
    -- 重置计数，清理进行中的入侵
    self.invasions_done = 0
    self._net_invasions_done:set(0)
    self.last_failed_day = nil  -- 重置同日限制
    self.invasion_start_time = nil
    self.last_combat_time = nil
    if self.boss and self.boss:IsValid() then
        self.boss:Remove()
    end
    if self.marker and self.marker:IsValid() then
        self.marker:Remove()
    end
    self.marker = nil
    self.active = false
    self._net_active:set(false)
    self.boss = nil
    self.origin = nil

    -- 调度第一次入侵（随机0.5~1.5天后）
    local total = TUNING.TOTAL_DAY_TIME or 480
    local delay = total * (0.5 + math.random())
    self.inst:DoTaskInTime(delay, function() self:TryStartInvasion() end)
end

-- 检查位置是否适合入侵（避开基地建筑、洞穴入口、传送台）
local function IsValidInvasionSpot(pos, min_distance_from_structures, min_distance_from_special)
    local x, y, z = pos:Get()

    -- 检查基地密集建筑（20格内）
    local structures = TheSim:FindEntities(x, y, z, min_distance_from_structures or 20, nil, {"INLIMBO", "NOCLICK", "FX"})
    local structure_count = 0
    for _, ent in ipairs(structures) do
        -- 计算玩家建造的结构（有workable组件的建筑、火堆、箱子等）
        if ent.components and (ent.components.workable or ent.components.container or ent.components.burnable) then
            if ent:HasTag("structure") or ent.prefab == "campfire" or ent.prefab == "firepit" or
               ent.prefab == "coldfire" or ent.prefab == "coldfirepit" or ent.prefab == "treasurechest" or
               ent.prefab == "icebox" or ent.prefab == "saltbox" then
                structure_count = structure_count + 1
            end
        end
    end

    -- 如果20格内有3个或更多建筑，认为是基地密集区域
    if structure_count >= 3 then
        return false, "基地建筑密集"
    end

    -- 检查洞穴入口和传送台（5格内）
    local special_entities = TheSim:FindEntities(x, y, z, min_distance_from_special or 5, nil, {"INLIMBO", "NOCLICK", "FX"})
    for _, ent in ipairs(special_entities) do
        -- 洞穴入口
        if ent.prefab == "cave_entrance" or ent.prefab == "cave_entrance_open" or ent.prefab == "cave_entrance_ruins" then
            return false, "洞穴入口附近"
        end
        -- 传送台相关
        if ent.prefab == "teleportato_base" or ent.prefab == "teleportato_potato" or
           ent.prefab == "teleportato_ring" or ent.prefab == "teleportato_box" or
           ent.prefab == "teleportato_crank" or ent.prefab == "teleportato_sw_base" then
            return false, "传送台附近"
        end
        -- 其他传送相关结构
        if ent.prefab == "multiplayer_portal" or ent.prefab == "multiplayer_portal_moonrock" or
           ent.prefab == "moonbase" or ent.prefab == "portal_entrance" then
            return false, "传送门附近"
        end
    end

    return true, "位置合适"
end

function SeasonWardenInvasion:TryStartInvasion()
    if not self.enabled or self.active then return end
    if self.invasions_done >= self.per_season then return end

    -- 检查是否在同日限制期内
    local current_day = math.floor(GetTime() / (TUNING.TOTAL_DAY_TIME or 480))
    if self.last_failed_day and self.last_failed_day == current_day then
        -- 同日不再追击，延迟到明天再试
        local time_to_next_day = (current_day + 1) * (TUNING.TOTAL_DAY_TIME or 480) - GetTime()
        self.inst:DoTaskInTime(time_to_next_day + 60, function() self:TryStartInvasion() end) -- 新一天开始后1分钟再试
        return
    end

    -- 选择随机玩家
    local candidates = {}
    for _,p in ipairs(AllPlayers or {}) do if IsValidPlayer(p) then table.insert(candidates, p) end end
    if #candidates == 0 then return end
    local player = candidates[math.random(#candidates)]
    local pos = player:GetPosition()

    -- 尝试找到合适的入侵位置（最多尝试10次）
    local spawnpt = nil
    local attempt_count = 0
    local max_attempts = 10

    while attempt_count < max_attempts do
        attempt_count = attempt_count + 1
        local offset = FindWalkableOffset(pos, math.random()*2*PI, math.random(8,15), 16, true)
        local test_pos = offset and (pos + offset) or pos

        local valid, reason = IsValidInvasionSpot(test_pos, 20, 5)
        if valid then
            spawnpt = test_pos
            break
        else
            print(string.format("[入侵] 位置不合适 (尝试%d/%d): %s", attempt_count, max_attempts, reason))
        end
    end

    -- 如果找不到合适位置，延迟重试
    if not spawnpt then
        print("[入侵] 未找到合适的入侵位置，1小时后重试")
        self.inst:DoTaskInTime(TUNING.TOTAL_DAY_TIME / 16, function() self:TryStartInvasion() end) -- 1小时后重试
        return
    end
    -- 预警FX（地面符文圈+染色闪光，复用moon_base与staffcastfx改色）
    local fx = SpawnPrefab("staff_castinglight")
    if fx then
        fx.Transform:SetPosition(spawnpt.x, 0, spawnpt.z)
        -- 设置预警特效颜色（橙红色警告）
        if fx.AnimState then
            fx.AnimState:SetMultColour(1, 0.5, 0.2, 1)
        end
        fx:DoTaskInTime(self.warn_secs, fx.Remove)
    end

    -- 额外的地面符文圈效果（复用moon_base视觉效果）
    local moon_fx = SpawnPrefab("staff_castinglight")  -- 使用相同特效但不同颜色
    if moon_fx then
        moon_fx.Transform:SetPosition(spawnpt.x, 0, spawnpt.z)
        if moon_fx.AnimState then
            moon_fx.AnimState:SetMultColour(0.8, 0.8, 1, 0.6) -- 淡蓝色符文圈效果
            moon_fx.Transform:SetScale(1.5, 1.5, 1.5) -- 稍大一些形成双重圈效果
        end
        moon_fx:DoTaskInTime(self.warn_secs, moon_fx.Remove)
    end

    -- 临时小地图标记（复用moonbase图标，warn_secs后清除）
    if self.marker and self.marker:IsValid() then self.marker:Remove() end
    self.marker = SpawnPrefab("invasion_marker")
    if self.marker and self.marker.entity and self.marker.entity.AddMiniMapEntity then
        self.marker.entity:AddMiniMapEntity()
        self.marker.MiniMapEntity:SetIcon("moonbase.png")  -- 使用moonbase图标
        self.marker.MiniMapEntity:SetPriority(1)
        self.marker.Transform:SetPosition(spawnpt.x, 0, spawnpt.z)
        self.inst:DoTaskInTime(self.warn_secs + 10, function()
            if self.marker and self.marker:IsValid() then self.marker:Remove() end
            self.marker = nil
        end)
    end
    self.active = true
    self._net_active:set(true)
    self.origin = {x=spawnpt.x, y=0, z=spawnpt.z}
    -- 地图标记和玩家通知
    if TheWorld.components.worldsettings_overrides == nil then
        TheNet:Announce(string.format("季冠树守入侵预警！%d秒后降临！", self.warn_secs), nil, nil)
    end
    if TheWorld.minimap ~= nil and TheWorld.minimap.MiniMap ~= nil then
        TheWorld.minimap.MiniMap:ShowArea(spawnpt.x, 0, spawnpt.z, 15)
    end

    -- 通知所有玩家
    for _, p in ipairs(AllPlayers or {}) do
        if p and p:IsValid() and p.components and p.components.talker then
            p.components.talker:Say(string.format("季冠树守将在%d秒后入侵！", self.warn_secs))
        end
    end

    print(string.format("[入侵] 季冠树守将在%d秒后在坐标(%.1f, %.1f)入侵，目标玩家：%s",
          self.warn_secs, spawnpt.x, spawnpt.z, player.name or "未知"))

    self.inst:DoTaskInTime(self.warn_secs, function() self:SpawnBossAt(spawnpt) end)
end

function SeasonWardenInvasion:SpawnBossAt(spawnpt)
    if not self.active then return end
    local boss = SpawnPrefab("boss_season_warden")
    if not boss then
        self.active = false
        self._net_active:set(false)
        return
    end
    boss.Transform:SetPosition(spawnpt.x, 0, spawnpt.z)
    boss._invasion = true

    -- 标记切换为战斗态图标（复用 hound.png）
    if self.marker and self.marker:IsValid() and self.marker.MiniMapEntity then
        self.marker.MiniMapEntity:SetIcon("hound.png")
        self.marker.MiniMapEntity:SetPriority(1)
        local x,y,z = boss.Transform:GetWorldPosition()
        self.marker.Transform:SetPosition(x,0,z)
    end

    -- 多人联机缩放：血量和掉落都*人数^0.5
    local player_count = #(AllPlayers or {})
    local multiplayer_scale = player_count > 1 and math.sqrt(player_count) or 1.0

    -- HP缩放（基础血量 * Boss血量倍率 * 入侵HP倍率 * 多人缩放）
    if boss.components and boss.components.health then
        local base = TUNING.SEASON_BOSS_HP or 8000
        local boss_hp_mult = _G.GetModConfigData("boss_hp_mult") or 1.0
        local mhp = math.max(1, math.floor(base * boss_hp_mult * self.hp_mul * multiplayer_scale))
        boss.components.health:SetMaxHealth(mhp)
        boss.components.health:SetPercent(1)
        -- 记录初始血量用于回血计算
        boss._invasion_initial_hp = mhp
    end

    -- 记录多人缩放倍率用于掉落计算
    boss._invasion_loot_scale = multiplayer_scale

    -- 记录入侵开始时间
    self.invasion_start_time = GetTime()
    self.last_combat_time = GetTime()

    -- 入侵Boss预警期间不攻击（设计文档要求）
    if boss.components.combat then
        boss.components.combat:SetTarget(nil)
        boss._invasion_peaceful = true
        -- 2秒后允许攻击（给玩家反应时间）
        boss:DoTaskInTime(2, function(inst)
            if inst and inst:IsValid() then
                inst._invasion_peaceful = nil
            end
        end)
    end

    -- 监听Boss的战斗事件来更新最后战斗时间
    boss:ListenForEvent("attacked", function(inst, data)
        if self.active and self.boss == inst then
            self.last_combat_time = GetTime()
        end
    end)

    boss:ListenForEvent("onattackother", function(inst, data)
        if self.active and self.boss == inst then
            self.last_combat_time = GetTime()
        end
    end)

    -- 日志输出
    print(string.format("[入侵] 季冠树守已生成！玩家数：%d，多人缩放：%.2f，血量：%d/%d",
          player_count, multiplayer_scale, boss.components.health.currenthealth, boss.components.health.maxhealth))

    -- 结算：监听死亡（成功）与距离（失败）
    boss:ListenForEvent("death", function(inst)
        self:OnBossDeath(inst)
    end)
    -- 优化：降低检查频率，减少性能消耗
    boss:DoPeriodicTask(2, function()
        self:CheckRadius()
        self:CheckCombatTimeout()  -- 检查脱战和超时
        if self.marker and self.marker:IsValid() then
            local x,y,z = boss.Transform:GetWorldPosition()
            self.marker.Transform:SetPosition(x,0,z)
        end
    end)
    -- 保底核心掉落（至少1个，应用多人缩放）
    boss:ListenForEvent("death", function(inst)
        local x,y,z = inst.Transform:GetWorldPosition()
        -- 掉落倍率：配置的掉落倍率 * 多人缩放，至少1个季芯
        local loot_scale = inst._invasion_loot_scale or 1.0
        local cores = math.max(1, math.floor(self.loot_mul * 2 * loot_scale + 0.5))
        for i=1,cores do
            local c = SpawnPrefab("season_core")
            if c then c.Transform:SetPosition(x + math.random(-2, 2), 0, z + math.random(-2, 2)) end
        end

        -- 额外随机掉落（应用多人缩放）
        if math.random() < (0.3 * loot_scale) then
            local extra_loot = {"red_cap", "royal_jelly"}
            local item = SpawnPrefab(extra_loot[math.random(#extra_loot)])
            if item then item.Transform:SetPosition(x + math.random(-2, 2), 0, z + math.random(-2, 2)) end
        end
    end)

    self.boss = boss
end

-- 数据持久化
function SeasonWardenInvasion:OnSave()
    return {
        invasions_done = self.invasions_done,
        last_failed_day = self.last_failed_day,
        active = self.active,
        invasion_start_time = self.invasion_start_time,
        last_combat_time = self.last_combat_time,
    }
end

function SeasonWardenInvasion:OnLoad(data)
    if not TheWorld.ismastersim then return end
    if data then
        self.invasions_done = data.invasions_done or 0
        self._net_invasions_done:set(self.invasions_done)
        self.last_failed_day = data.last_failed_day
        self.active = data.active or false
        self._net_active:set(self.active)
        self.invasion_start_time = data.invasion_start_time
        self.last_combat_time = data.last_combat_time
    end
end

function SeasonWardenInvasion:CheckRadius()
    if not self.active or not self.boss or not self.boss:IsValid() then return end
    if not self.origin then return end
    local x,y,z = self.boss.Transform:GetWorldPosition()
    local dx = x - self.origin.x
    local dz = z - self.origin.z
    local d2 = dx*dx + dz*dz
    if d2 > (self.battle_radius * self.battle_radius) then
        -- 超出半径：Boss返回原点并回血至开场血量的50%
        self:HandleInvasionFailure("Boss超出战斗半径")
    end
end

-- 检查脱战和超时情况
function SeasonWardenInvasion:CheckCombatTimeout()
    if not self.active or not self.boss or not self.boss:IsValid() then return end
    if not self.invasion_start_time or not self.last_combat_time then return end

    local current_time = GetTime()
    local time_since_start = current_time - self.invasion_start_time
    local time_since_combat = current_time - self.last_combat_time

    -- 检查总体超时（设计文档未明确，设为15分钟）
    if time_since_start > (TUNING.SEASON_WARDEN_INVASION_TOTAL_TIMEOUT or 900) then
        self:HandleInvasionFailure("入侵总体超时")
        return
    end

    -- 检查脱战超时（5分钟无战斗）
    if time_since_combat > self.combat_timeout then
        self:HandleInvasionFailure("玩家脱战超时")
        return
    end
end

-- 统一处理入侵失败的逻辑
function SeasonWardenInvasion:HandleInvasionFailure(reason)
    if not self.active or not self.boss or not self.boss:IsValid() then return end

    print(string.format("[入侵] 入侵失败：%s", reason))

    -- Boss返回原点并回血至开场血量的50%
    self.boss.Transform:SetPosition(self.origin.x, 0, self.origin.z)

    -- 回血至开场血量的50%
    if self.boss.components and self.boss.components.health and self.boss._invasion_initial_hp then
        local target_hp = self.boss._invasion_initial_hp * 0.5
        local current_hp = self.boss.components.health.currenthealth
        if current_hp < target_hp then
            self.boss.components.health:SetCurrentHealth(target_hp)
            -- 播放回血特效
            local fx = SpawnPrefab("staff_castinglight")
            if fx then
                fx.Transform:SetPosition(self.boss.Transform:GetWorldPosition())
                if fx.AnimState then
                    fx.AnimState:SetMultColour(0.2, 1, 0.2, 1) -- 绿色回血特效
                end
            end
        end
    end

    -- 设置同日不再追击标记
    self.last_failed_day = math.floor(GetTime() / (TUNING.TOTAL_DAY_TIME or 480))

    -- 通知玩家
    local message = ""
    if reason == "Boss超出战斗半径" then
        message = "季冠树守回归了原点并恢复了部分生命力..."
    elseif reason == "玩家脱战超时" then
        message = "季冠树守因无人应战而撤退了..."
    elseif reason == "入侵总体超时" then
        message = "季冠树守因战斗时间过长而撤退了..."
    end

    for _, p in ipairs(AllPlayers or {}) do
        if p and p:IsValid() and p.components and p.components.talker then
            p.components.talker:Say(message)
        end
    end

    -- 结束这次入侵，安排2天后重试
    self.boss:Remove()
    self.boss = nil
    self.active = false
    self._net_active:set(false)
    self.invasion_start_time = nil
    self.last_combat_time = nil
    self:ScheduleRetry()
end

function SeasonWardenInvasion:OnBossDeath(inst)
    if not TheWorld.ismastersim then return end
    -- 本季完成次数+1
    self.invasions_done = self.invasions_done + 1
    self._net_invasions_done:set(self.invasions_done)
    print(string.format("[入侵] 季冠树守被击败！本季完成次数：%d/%d", self.invasions_done, self.per_season))

    if self.marker and self.marker:IsValid() then self.marker:Remove() end
    self.marker = nil
    self.active = false
    self._net_active:set(false)
    self.boss = nil
    self.origin = nil
    self.invasion_start_time = nil
    self.last_combat_time = nil

    -- 通知玩家
    for _, p in ipairs(AllPlayers or {}) do
        if p and p:IsValid() and p.components and p.components.talker then
            p.components.talker:Say(string.format("季冠树守已被击败！本季剩余入侵次数：%d", self.per_season - self.invasions_done))
        end
    end

    -- 若未达目标次数，随机延迟后继续下一次入侵
    if self.invasions_done < self.per_season then
        local total = TUNING.TOTAL_DAY_TIME or 480
        local delay = total * (0.5 + math.random())
        print(string.format("[入侵] 将在%.1f小时后进行下一次入侵", delay / (total / 16)))
        self.inst:DoTaskInTime(delay, function() self:TryStartInvasion() end)
    else
        print("[入侵] 本季入侵任务已完成")
    end
end

function SeasonWardenInvasion:ScheduleRetry()
    local total = TUNING.TOTAL_DAY_TIME or 480
    local delay = total * (self.respawn_days or 2)
    print(string.format("[入侵] Boss逃脱，将在%d天后重试入侵", self.respawn_days or 2))

    -- 通知玩家
    for _, p in ipairs(AllPlayers or {}) do
        if p and p:IsValid() and p.components and p.components.talker then
            p.components.talker:Say(string.format("季冠树守逃脱了，%d天后将再次入侵！", self.respawn_days or 2))
        end
    end

    self.inst:DoTaskInTime(delay, function() self:TryStartInvasion() end)
end

return SeasonWardenInvasion
