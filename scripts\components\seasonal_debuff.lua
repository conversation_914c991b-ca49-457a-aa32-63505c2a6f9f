local _G = GLOBAL
local Class = _G.Class
local TheWorld = _G.TheWorld
local net_float = _G.net_float
local net_ushort = _G.net_ushort

local SeasonalDebuff = Class(function(self, inst)
    self.inst = inst
    self.slow_mult = 1.0
    self.slow_duration = 0
    self.slow_task = nil

    -- 灼伤效果
    self.burn_damage = 0
    self.burn_duration = 0
    self.burn_task = nil
    self.burn_tick_task = nil

    -- 网络同步
    if not TheWorld.ismastersim then
        self._slow_mult = net_float(inst.GUID, "seasonal_debuff.slow_mult", "slow_mult_dirty")
        self._slow_duration = net_ushort(inst.GUID, "seasonal_debuff.slow_duration", "slow_duration_dirty")
        self._burn_damage = net_float(inst.GUID, "seasonal_debuff.burn_damage", "burn_damage_dirty")
        self._burn_duration = net_ushort(inst.GUID, "seasonal_debuff.burn_duration", "burn_duration_dirty")

        inst:ListenForEvent("slow_mult_dirty", function()
            self.slow_mult = self._slow_mult:value()
            self:UpdateVisuals()
        end)

        inst:ListenForEvent("slow_duration_dirty", function()
            self.slow_duration = self._slow_duration:value()
        end)

        inst:ListenForEvent("burn_damage_dirty", function()
            self.burn_damage = self._burn_damage:value()
            self:UpdateVisuals()
        end)

        inst:ListenForEvent("burn_duration_dirty", function()
            self.burn_duration = self._burn_duration:value()
        end)
        return
    end

    -- 服务端初始化网络变量
    self._slow_mult = net_float(inst.GUID, "seasonal_debuff.slow_mult", "slow_mult_dirty")
    self._slow_duration = net_ushort(inst.GUID, "seasonal_debuff.slow_duration", "slow_duration_dirty")
    self._burn_damage = net_float(inst.GUID, "seasonal_debuff.burn_damage", "burn_damage_dirty")
    self._burn_duration = net_ushort(inst.GUID, "seasonal_debuff.burn_duration", "burn_duration_dirty")
end)

function SeasonalDebuff:ApplySlow(multiplier, duration, source_is_boss)
    if not TheWorld.ismastersim then return end

    -- 错误处理：检查实例和参数有效性
    if not self.inst or not self.inst:IsValid() then
        print("[SeasonWorkshop] Error: Invalid debuff instance in ApplySlow")
        return
    end

    if not multiplier or not duration or multiplier < 0 or multiplier > 1 or duration < 0 then
        print("[SeasonWorkshop] Error: Invalid slow parameters - mult:" .. tostring(multiplier) .. " dur:" .. tostring(duration))
        return
    end

    -- 护盾状态下的异常免疫检查
    if self.inst:HasTag("season_shield_immune") then
        -- 护盾状态下减速效果减弱75%
        multiplier = multiplier + (1 - multiplier) * 0.75  -- 例如0.8变为0.95
        duration = duration * 0.25  -- 持续时间减少75%
        print(string.format("[SeasonWorkshop] Slow effect reduced by shield immunity: %.2f -> %.2f, %.1fs -> %.1fs",
              multiplier + (1 - multiplier) * 0.75, multiplier, duration * 4, duration))
    end

    -- Boss减免效果
    if self.inst:HasTag("epic") and source_is_boss then
        local old_mult = multiplier
        local old_dur = duration
        multiplier = math.max(0.85, multiplier + 0.05) -- -20%变为-15%
        duration = duration * 0.75 -- 2秒变为1.5秒
        print(string.format("[SeasonWorkshop] Boss resistance applied: %.2f -> %.2f, %.1fs -> %.1fs",
              old_mult, multiplier, old_dur, duration))
    end

    -- 检查是否已有其他减速效果，确保总减速不超过50%
    local current_mult = 1.0
    if self.inst.components and self.inst.components.locomotor then
        -- 获取当前所有外部速度修正
        local external_mults = self.inst.components.locomotor.external_speed_multipliers or {}
        for source, mult in pairs(external_mults) do
            if source ~= "seasonal_slow" then -- 排除我们即将设置的
                current_mult = current_mult * mult
            end
        end
    end

    -- 计算叠加后的总倍率，确保不低于0.5（即不超过50%减速）
    local combined_mult = current_mult * multiplier
    if combined_mult < 0.5 then
        multiplier = 0.5 / current_mult
        multiplier = math.max(0.5, multiplier) -- 确保不会出现负值或过小值
    end

    self.slow_mult = multiplier
    self.slow_duration = duration

    -- 同步到客户端
    self._slow_mult:set(multiplier)
    self._slow_duration:set(duration)

    -- 应用移动速度减益
    if self.inst.components and self.inst.components.locomotor then
        self.inst.components.locomotor:SetExternalSpeedMultiplier(self.inst, "seasonal_slow", multiplier)
    else
        print("[SeasonWorkshop] Warning: Target has no locomotor component for slow effect")
    end

    -- 清理旧任务
    if self.slow_task then
        self.slow_task:Cancel()
        self.slow_task = nil
    end

    -- 设置持续时间
    self.slow_task = self.inst:DoTaskInTime(duration, function()
        if self.inst and self.inst:IsValid() then
            self:ClearSlow()
        else
            print("[SeasonWorkshop] Warning: Debuff instance became invalid during slow effect")
        end
    end)

    if not self.slow_task then
        print("[SeasonWorkshop] Error: Failed to create slow duration task")
    end

    -- 视觉效果
    self:UpdateVisuals()

    print(string.format("[SeasonWorkshop] Slow applied: %.2f speed for %.1fs", multiplier, duration))
end

function SeasonalDebuff:ClearSlow()
    if not TheWorld.ismastersim then return end

    self.slow_mult = 1.0
    self.slow_duration = 0

    -- 同步到客户端
    self._slow_mult:set(1.0)
    self._slow_duration:set(0)

    -- 移除移动速度减益
    if self.inst.components and self.inst.components.locomotor then
        self.inst.components.locomotor:RemoveExternalSpeedMultiplier(self.inst, "seasonal_slow")
    end

    -- 清理任务
    if self.slow_task then
        self.slow_task:Cancel()
        self.slow_task = nil
    end

    -- 更新视觉效果
    self:UpdateVisuals()
end

function SeasonalDebuff:ApplyBurn(damage_per_second, duration)
    if not TheWorld.ismastersim then return end

    -- 护盾状态下的异常免疫检查
    if self.inst:HasTag("season_shield_immune") then
        -- 护盾状态下灼伤效果减弱75%
        damage_per_second = damage_per_second * 0.25
        duration = duration * 0.25
    end

    self.burn_damage = damage_per_second
    self.burn_duration = duration

    -- 同步到客户端
    self._burn_damage:set(damage_per_second)
    self._burn_duration:set(duration)

    -- 清理旧任务
    if self.burn_task then
        self.burn_task:Cancel()
    end
    if self.burn_tick_task then
        self.burn_tick_task:Cancel()
    end

    -- 每秒造成伤害
    self.burn_tick_task = self.inst:DoPeriodicTask(1, function()
        if self.inst.components and self.inst.components.health then
            self.inst.components.health:DoDelta(-self.burn_damage, nil, "seasonal_burn")
        end
    end)

    -- 设置持续时间
    self.burn_task = self.inst:DoTaskInTime(duration, function()
        self:ClearBurn()
    end)

    -- 视觉效果
    self:UpdateVisuals()
end

function SeasonalDebuff:ClearBurn()
    if not TheWorld.ismastersim then return end

    self.burn_damage = 0
    self.burn_duration = 0

    -- 同步到客户端
    self._burn_damage:set(0)
    self._burn_duration:set(0)

    -- 清理任务
    if self.burn_task then
        self.burn_task:Cancel()
        self.burn_task = nil
    end
    if self.burn_tick_task then
        self.burn_tick_task:Cancel()
        self.burn_tick_task = nil
    end

    -- 更新视觉效果
    self:UpdateVisuals()
end

function SeasonalDebuff:UpdateVisuals()
    if not self.inst.AnimState then return end

    local r, g, b = 0, 0, 0

    -- 减速效果：蓝色调
    if self.slow_mult < 1.0 then
        local intensity = (1.0 - self.slow_mult) * 0.3 -- 最大30%蓝色调
        b = intensity
    end

    -- 灼伤效果：红色调
    if self.burn_damage > 0 then
        local intensity = math.min(self.burn_damage / 5, 0.3) -- 最大30%红色调
        r = intensity
    end

    self.inst.AnimState:SetAddColour(r, g, b, 0)
end

function SeasonalDebuff:ClearVisuals()
    if self.inst.AnimState then
        self.inst.AnimState:SetAddColour(0, 0, 0, 0)
    end
end

function SeasonalDebuff:GetSlowMultiplier()
    return self.slow_mult
end

function SeasonalDebuff:IsSlowed()
    return self.slow_mult < 1.0
end

function SeasonalDebuff:GetBurnDamage()
    return self.burn_damage
end

function SeasonalDebuff:IsBurning()
    return self.burn_damage > 0
end

return SeasonalDebuff
