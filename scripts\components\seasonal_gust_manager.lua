local _G = GLOBAL
local Class = _G.Class
local TheWorld = _G.TheWorld
local TUNING = _G.TUNING
local AllPlayers = _G.AllPlayers
local net_bool = _G.net_bool
local net_string = _G.net_string
local net_float = _G.net_float

local SeasonalGust = Class(function(self, inst)
    self.inst = inst
    self.enabled = true
    self.events_left = 0
    self.scheduled = {}
    self.active_event = nil -- 当前活跃的事件
    self.original_colour = nil -- 保存原始世界颜色
    self.next_event_time = 0 -- 下次事件时间

    -- 网络同步变量
    if not TheWorld.ismastersim then
        self._net_active = net_bool(inst.GUID, "seasonal_gust.active", "gust_active_dirty")
        self._net_season = net_string(inst.GUID, "seasonal_gust.season", "gust_season_dirty")
        self._net_remaining = net_float(inst.GUID, "seasonal_gust.remaining", "gust_remaining_dirty")
        self._net_events_left = net_tinybyte(inst.GUID, "seasonal_gust.events_left", "gust_events_left_dirty")
        self._net_next_event_time = net_float(inst.GUID, "seasonal_gust.next_event", "gust_next_event_dirty")

        -- 客户端监听事件状态变化
        inst:ListenForEvent("gust_active_dirty", function()
            if not self.inst or not self.inst:IsValid() then
                print("[SeasonWorkshop] Warning: Invalid gust manager in active sync")
                return
            end

            local is_active = self._net_active:value()
            if is_active and not self.active_event then
                -- 事件开始
                local season = self._net_season:value()
                if season and season ~= "" then
                    self:ApplyWorldTint(season)
                    self.active_event = { season = season }
                end
            elseif not is_active and self.active_event then
                -- 事件结束
                self:RestoreWorldTint()
                self.active_event = nil
            end
        end)

        inst:ListenForEvent("gust_events_left_dirty", function()
            if self.inst and self.inst:IsValid() then
                self.events_left = self._net_events_left:value()
            end
        end)

        inst:ListenForEvent("gust_next_event_dirty", function()
            if self.inst and self.inst:IsValid() then
                self.next_event_time = self._net_next_event_time:value()
            end
        end)

        return
    end

    -- 服务端初始化网络变量
    self._net_active = net_bool(inst.GUID, "seasonal_gust.active", "gust_active_dirty")
    self._net_season = net_string(inst.GUID, "seasonal_gust.season", "gust_season_dirty")
    self._net_remaining = net_float(inst.GUID, "seasonal_gust.remaining", "gust_remaining_dirty")
    self._net_events_left = net_tinybyte(inst.GUID, "seasonal_gust.events_left", "gust_events_left_dirty")
    self._net_next_event_time = net_float(inst.GUID, "seasonal_gust.next_event", "gust_next_event_dirty")

    self._net_active:set(false)
    self._net_season:set("")
    self._net_remaining:set(0)
    self._net_events_left:set(0)
    self._net_next_event_time:set(0)

    inst:WatchWorldState("season", function()
        self:OnSeasonChanged()
    end)
    inst:DoTaskInTime(0, function() self:OnSeasonChanged(true) end)
end)

local function SpawnOrbsNearPlayer(player)
    if not player or not player:IsValid() then return end
    local season = TheWorld.state.season or "autumn"
    local orb_pref = "season_orb_"..season
    local count = math.random(3,5)

    -- 让玩家说"天降神珠"
    if player.components and player.components.talker then
        player.components.talker:Say("天降神珠！", 2)
    end

    -- 在玩家附近生成宝珠
    for i=1,count do
        local x,y,z = player.Transform:GetWorldPosition()
        local offset = FindWalkableOffset(player:GetPosition(), math.random()*2*PI, math.random(2,5), 8, true)
        if offset ~= nil then
            x = x + offset.x
            z = z + offset.z
        end
        local ent = SpawnPrefab(orb_pref)
        if ent then
            ent.Transform:SetPosition(x, 0, z)
        end
    end
end

-- 应用世界变色效果
function SeasonalGust:ApplyWorldTint(season)
    -- 简化实现：使用PostProcessor来实现轻度变色效果
    -- 这是一个更安全的方法，避免直接操作colourcube
    if not TheWorld then return end

    -- 保存原始状态
    if not self.original_colour then
        self.original_colour = true -- 标记已保存
    end

    -- 根据季节设置轻度色调调整
    local tint_values = {
        spring = {r = 0.9, g = 1.1, b = 0.9}, -- 轻微绿色调
        summer = {r = 1.1, g = 1.0, b = 0.8}, -- 轻微橙色调
        autumn = {r = 1.0, g = 0.95, b = 0.8}, -- 轻微褐色调
        winter = {r = 0.9, g = 0.95, b = 1.1}  -- 轻微蓝色调
    }

    local tint = tint_values[season] or tint_values["autumn"]

    -- 通过PostProcessor应用色调（如果可用）
    if TheWorld.components.postprocessor then
        TheWorld.components.postprocessor:SetColourModifier(tint.r, tint.g, tint.b, 1)
    end

    -- 为所有玩家显示提示信息
    for _, p in ipairs(AllPlayers or {}) do
        if p and p:IsValid() and p.components and p.components.talker then
            local season_names = {
                spring = "春季",
                summer = "夏季",
                autumn = "秋季",
                winter = "冬季"
            }
            p.components.talker:Say("世界被" .. (season_names[season] or "神秘") .. "的气息笼罩...", 2)
        end
    end
end

-- 恢复世界原始颜色
function SeasonalGust:RestoreWorldTint()
    if not TheWorld or not self.original_colour then return end

    -- 恢复默认色调
    if TheWorld.components.postprocessor then
        TheWorld.components.postprocessor:SetColourModifier(1, 1, 1, 1)
    end

    self.original_colour = nil
end

-- 开始季风乱流事件
function SeasonalGust:StartEvent()
    if not TheWorld.ismastersim then return end

    -- 错误处理：检查状态
    if not self.inst or not self.inst:IsValid() then
        print("[SeasonWorkshop] Error: Invalid gust manager in StartEvent")
        return
    end

    if not self.enabled or self.active_event then
        print("[SeasonWorkshop] Info: Gust event skipped - disabled or already active")
        return
    end

    local season = TheWorld.state.season or "autumn"

    -- 验证季节有效性
    local valid_seasons = {spring = true, summer = true, autumn = true, winter = true}
    if not valid_seasons[season] then
        print("[SeasonWorkshop] Error: Invalid season for gust event: " .. tostring(season))
        season = "autumn" -- 回退到默认季节
    end

    -- 事件开始提示
    local player_count = 0
    for _, p in ipairs(AllPlayers or {}) do
        if p and p:IsValid() and p.components and p.components.talker then
            p.components.talker:Say("季风乱流开始了！", 3)
            player_count = player_count + 1
        end
    end

    print(string.format("[SeasonWorkshop] Gust event started for %d players in %s season", player_count, season))

    -- 应用世界变色
    self:ApplyWorldTint(season)

    -- 延迟一秒后在每位玩家附近生成季节宝珠（让开始提示先显示）
    self.inst:DoTaskInTime(1, function()
        -- 确保在联机服务器时为每个在线玩家都刷新宝珠
        local players = AllPlayers or {}
        if #players == 0 and ThePlayer then
            -- 单机模式的后备方案
            players = {ThePlayer}
        end

        for _, p in ipairs(players) do
            if p and p:IsValid() and not p:HasTag("playerghost") then
                SpawnOrbsNearPlayer(p)
            end
        end
    end)

    -- 设置事件持续时间（半天至1天，配置项）
    local duration_config = _G.GetModConfigData("gust_duration") or "medium"
    local duration_hours = 12 -- 默认半天
    if duration_config == "short" then
        duration_hours = 6  -- 短：6小时
    elseif duration_config == "long" then
        duration_hours = 24 -- 长：1天
    end

    local duration_seconds = duration_hours * 60 -- 简化：1小时=60秒（游戏时间）

    -- 标记事件为活跃状态
    self.active_event = {
        season = season,
        start_time = GetTime(),
        duration = duration_seconds
    }

    -- 同步事件状态到客户端
    self._net_active:set(true)
    self._net_season:set(season)
    self._net_remaining:set(duration_seconds)

    -- 设置事件结束任务
    self.inst:DoTaskInTime(duration_seconds, function()
        self:EndEvent()
    end)

    -- 定期更新剩余时间
    self.update_task = self.inst:DoPeriodicTask(10, function()
        if self.active_event then
            local remaining = self.active_event.start_time + self.active_event.duration - GetTime()
            self._net_remaining:set(math.max(0, remaining))
        end
    end)
end

-- 结束季风乱流事件
function SeasonalGust:EndEvent()
    if not TheWorld.ismastersim then return end
    if not self.active_event then return end

    -- 事件结束提示
    for _, p in ipairs(AllPlayers or {}) do
        if p and p:IsValid() and p.components and p.components.talker then
            p.components.talker:Say("季风乱流结束了。", 3)
        end
    end

    -- 恢复世界颜色
    self:RestoreWorldTint()

    -- 发送事件结束通知，让所有宝珠消失
    TheWorld:PushEvent("seasonal_gust_ended")

    -- 同步事件结束状态到客户端
    self._net_active:set(false)
    self._net_season:set("")
    self._net_remaining:set(0)

    -- 清理更新任务
    if self.update_task then
        self.update_task:Cancel()
        self.update_task = nil
    end

    -- 清理活跃事件标记
    self.active_event = nil
end

function SeasonalGust:TriggerEvent()
    self:StartEvent()
end

function SeasonalGust:OnSeasonChanged(initial)
    if not TheWorld.ismastersim then return end

    -- 错误处理：检查实例有效性
    if not self.inst or not self.inst:IsValid() then
        print("[SeasonWorkshop] Error: Invalid gust manager instance in OnSeasonChanged")
        return
    end

    -- 读取配置：每季触发次数
    local per_season = _G.GetModConfigData("gust_frequency") or 2
    self.events_left = per_season

    -- 同步到客户端
    self._net_events_left:set(per_season)

    -- 清理已有计划
    for _, t in ipairs(self.scheduled) do
        if t and t.Cancel then
            t:Cancel()
        end
    end
    self.scheduled = {}

    -- 计划在本季触发事件，随机在当前季内的 [min,max] 天后触发
    local min_days = TUNING.SEASON_EVENT_MIN_DAYS or 8
    local max_days = TUNING.SEASON_EVENT_MAX_DAYS or 12
    local total_time = _G.TUNING.TOTAL_DAY_TIME or 480

    for i=1,per_season do
        local days = math.random(min_days, max_days)
        local delay = days * total_time * 0.25 -- 简化：缩短等待，加快测试节奏（1/4），后续可调为1.0
        local event_time = GetTime() + delay

        -- 记录下次事件时间（用于客户端显示）
        if i == 1 then
            self.next_event_time = event_time
            self._net_next_event_time:set(event_time)
        end

        local task = self.inst:DoTaskInTime(delay, function()
            if self.inst and self.inst:IsValid() then
                self:TriggerEvent()
            else
                print("[SeasonWorkshop] Warning: Gust manager became invalid during scheduled event")
            end
        end)

        if task then
            table.insert(self.scheduled, task)
        else
            print("[SeasonWorkshop] Error: Failed to schedule gust event")
        end
    end
end

return SeasonalGust
